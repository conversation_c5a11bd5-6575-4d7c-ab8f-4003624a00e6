# 古琴定制功能所需图片资源

## 概述
此文档列出了古琴定制功能所需的所有图片资源。在实际使用前，需要将这些图片添加到 `Assets.xcassets` 中。

## 古琴形制图片 (Shape Images)
这些图片应该是高分辨率的PNG格式，带有透明背景，尺寸建议为800x300像素。

### 形制蒙版图片 (用于材质蒙版)
- `guqin_mask_fuxi.png` - 伏羲式蒙版
- `guqin_mask_hundun.png` - 混沌式蒙版
- `guqin_mask_zhenghe.png` - 正和式蒙版
- `guqin_mask_zhongni.png` - 仲尼式蒙版
- `guqin_mask_liezi.png` - 列子式蒙版

### 形制轮廓图片 (用于显示琴身轮廓)
- `guqin_outline_fuxi.png` - 伏羲式轮廓
- `guqin_outline_hundun.png` - 混沌式轮廓
- `guqin_outline_zhenghe.png` - 正和式轮廓
- `guqin_outline_zhongni.png` - 仲尼式轮廓
- `guqin_outline_liezi.png` - 列子式轮廓

### 形制图标 (用于选择按钮)
- `guqin_icon_fuxi.png` - 伏羲式图标 (64x64)
- `guqin_icon_hundun.png` - 混沌式图标 (64x64)
- `guqin_icon_zhenghe.png` - 正和式图标 (64x64)
- `guqin_icon_zhongni.png` - 仲尼式图标 (64x64)
- `guqin_icon_liezi.png` - 列子式图标 (64x64)

## 材质纹理图片 (Material Textures)
这些图片用于通过形制蒙版叠加在古琴上，创建不同的材质效果。

### 材质纹理图片
- `material_texture_black_lacquer.png` - 黑漆纹理
- `material_texture_brown_lacquer.png` - 棕漆纹理
- `material_texture_red_lacquer.png` - 红漆纹理
- `material_texture_wood_grain.png` - 木纹纹理
- `material_texture_flower_decoration.png` - 花饰纹理

### 材质缩略图 (用于选择按钮)
- `material_thumbnail_black_lacquer.png` - 黑漆缩略图 (60x60)
- `material_thumbnail_brown_lacquer.png` - 棕漆缩略图 (60x60)
- `material_thumbnail_red_lacquer.png` - 红漆缩略图 (60x60)
- `material_thumbnail_wood_grain.png` - 木纹缩略图 (60x60)
- `material_thumbnail_flower_decoration.png` - 花饰缩略图 (60x60)

## 琴弦图片 (Strings Images)
这些图片应该与古琴形制图片尺寸匹配，用于在ZStack最顶层显示琴弦。

### 琴弦基础图片
- `guqin_strings_seven.png` - 七弦琴弦 (800x300, 透明背景)
- `guqin_strings_five.png` - 五弦琴弦 (800x300, 透明背景)

### 琴弦图标 (用于选择按钮)
- `guqin_strings_icon_seven.png` - 七弦图标 (64x64)
- `guqin_strings_icon_five.png` - 五弦图标 (64x64)

### 琴弦材质图片
- `strings_material_silk.png` - 丝弦材质效果 (800x300, 透明背景)
- `strings_material_nylon.png` - 尼龙弦材质效果 (800x300, 透明背景)
- `strings_material_steel.png` - 钢弦材质效果 (800x300, 透明背景)

## 临时解决方案
在正式图片资源准备好之前，代码将使用以下策略：
1. 使用现有的 `horizontal_guqin` 图片作为所有形制的占位符
2. 使用系统颜色作为材质的占位符
3. 使用系统图标作为各种按钮的占位符

## 添加资源到项目的步骤
1. 在 `Assets.xcassets/Images` 文件夹中为每个图片创建新的 `.imageset`
2. 将对应的PNG文件拖拽到相应的imageset中
3. 确保在代码中使用的图片名称与Assets中的名称完全匹配

## 图片设计建议
1. **古琴形制图片**: 应该是简洁的轮廓图，便于材质叠加
2. **材质纹理**: 应该是高质量的纹理，支持不同的混合模式
3. **图标**: 应该简洁明了，符合iOS设计规范
4. **所有图片**: 建议提供@2x和@3x版本以支持不同分辨率的设备
