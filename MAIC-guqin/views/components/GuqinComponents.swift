//
//  GuqinComponents.swift
//  MAIC-guqin
//
//  Created by AI Assistant on 2025/7/4.
//

import SwiftUI

// MARK: - 古琴预览视图
struct GuqinPreviewView: View {
    let shapeImageName: String
    let materialImageName: String?
    let materialColor: Color?
    let materialBlendMode: BlendMode
    let stringsImageName: String
    
    var body: some View {
        ZStack {
            // 最底层：古琴形制轮廓
            Image(shapeImageName)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxWidth: 280, maxHeight: 400)
            
            // 中间层：材质层
            if let materialImageName = materialImageName {
                // 纹理材质
                Image(materialImageName)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .mask(
                        Image(shapeImageName)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                    )
                    .blendMode(materialBlendMode)
                    .frame(maxWidth: 280, maxHeight: 400)
            } else if let materialColor = materialColor {
                // 纯色材质
                materialColor
                    .mask(
                        Image(shapeImageName)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                    )
                    .frame(maxWidth: 280, maxHeight: 400)
            }
            
            // 最顶层：琴弦
            Image(stringsImageName)
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(maxWidth: 280, maxHeight: 400)
        }
        .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
        .scaleEffect(1.0)
        .animation(.easeInOut(duration: 0.3), value: shapeImageName)
        .animation(.easeInOut(duration: 0.3), value: materialImageName)
        .animation(.easeInOut(duration: 0.3), value: stringsImageName)
    }
}

// MARK: - 选项缩略图按钮
struct OptionThumbnailButton: View {
    let thumbnailImage: Image?
    let thumbnailColor: Color?
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    private let buttonSize: CGFloat = 60
    
    var body: some View {
        Button(action: {
            action()
        }) {
            VStack(spacing: 4) {
                // 缩略图区域
                ZStack {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.1))
                        .frame(width: buttonSize, height: buttonSize)
                    
                    if let thumbnailImage = thumbnailImage {
                        thumbnailImage
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: buttonSize - 8, height: buttonSize - 8)
                            .clipShape(RoundedRectangle(cornerRadius: 6))
                    } else if let thumbnailColor = thumbnailColor {
                        RoundedRectangle(cornerRadius: 6)
                            .fill(thumbnailColor)
                            .frame(width: buttonSize - 8, height: buttonSize - 8)
                    }
                }
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(
                            isSelected ? Color.blue : Color.clear,
                            lineWidth: isSelected ? 2 : 0
                        )
                )
                .scaleEffect(isSelected ? 1.05 : 1.0)
                
                // 标题文字
                Text(title)
                    .font(.caption2)
                    .foregroundColor(isSelected ? .blue : .secondary)
                    .lineLimit(1)
                    .frame(width: buttonSize)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - 类别图标按钮
struct CategoryIconButton: View {
    let category: CustomizationCategory
    let isSelected: Bool
    let action: () -> Void
    
    private let iconSize: CGFloat = 44
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                ZStack {
                    Circle()
                        .fill(isSelected ? Color.blue.opacity(0.2) : Color.gray.opacity(0.1))
                        .frame(width: iconSize, height: iconSize)
                    
                    Image(systemName: systemIconName)
                        .font(.system(size: 20, weight: .medium))
                        .foregroundColor(isSelected ? .blue : .secondary)
                }
                .scaleEffect(isSelected ? 1.1 : 1.0)
                
                Text(category.rawValue)
                    .font(.caption2)
                    .foregroundColor(isSelected ? .blue : .secondary)
                    .lineLimit(1)
            }
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
    
    private var systemIconName: String {
        switch category {
        case .shape:
            return "rectangle.3.group"
        case .material:
            return "paintbrush.fill"
        case .inscription:
            return "textformat.abc"
        case .strings:
            return "waveform"
        case .decoration:
            return "sparkles"
        case .tuning:
            return "tuningfork"
        }
    }
}

// MARK: - 定制选项选择器
struct CustomizationOptionSelector: View {
    @ObservedObject var viewModel: GuqinCustomizationViewModel
    
    var body: some View {
        VStack(spacing: 12) {
            // 当前类别标题
            HStack {
                Text(viewModel.selectedCategory.rawValue)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("选择\(viewModel.selectedCategory.rawValue)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal)
            
            // 选项滚动视图
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    switch viewModel.selectedCategory {
                    case .shape:
                        ForEach(GuqinShape.allCases) { shape in
                            OptionThumbnailButton(
                                thumbnailImage: Image(shape.iconName),
                                thumbnailColor: nil,
                                title: shape.rawValue,
                                isSelected: viewModel.currentConfiguration.shape == shape
                            ) {
                                viewModel.selectShape(shape)
                            }
                        }
                        
                    case .material:
                        ForEach(GuqinMaterialType.allCases) { material in
                            OptionThumbnailButton(
                                thumbnailImage: material.imageName != nil ? Image(material.thumbnailName) : nil,
                                thumbnailColor: material.color,
                                title: material.rawValue,
                                isSelected: viewModel.currentConfiguration.material == material
                            ) {
                                viewModel.selectMaterial(material)
                            }
                        }
                        
                    case .strings:
                        ForEach(GuqinStringsCount.allCases) { strings in
                            OptionThumbnailButton(
                                thumbnailImage: Image(strings.iconName),
                                thumbnailColor: nil,
                                title: strings.rawValue,
                                isSelected: viewModel.currentConfiguration.strings == strings
                            ) {
                                viewModel.selectStrings(strings)
                            }
                        }
                        
                    default:
                        Text("敬请期待")
                            .foregroundColor(.secondary)
                            .frame(maxWidth: .infinity)
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(UIColor.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: -2)
        )
    }
}
