//
//  GuqinOptionModel.swift
//  MAIC-guqin
//
//  Created by AI Assistant on 2025/7/4.
//

import SwiftUI

// MARK: - 古琴形制枚举
enum GuqinShape: String, CaseIterable, Identifiable {
    case fuxi = "伏羲式"
    case zhongni = "仲尼式"
    case luoxia = "落霞式"
    case jiaowei = "焦尾式"
    
    var id: String { self.rawValue }
    
    /// 对应的基础形制图片名称
    var imageName: String {
        // 临时使用现有的古琴图片作为占位符
        return "horizontal_guqin"
    }

    /// 形制图标名称（用于选择按钮）
    var iconName: String {
        // 临时使用现有的古琴图片作为占位符
        return "horizontal_guqin"
    }
}

// MARK: - 古琴材质类型枚举
enum GuqinMaterialType: String, CaseIterable, Identifiable {
    case blackLacquer = "黑漆"
    case brownLacquer = "棕漆"
    case redLacquer = "红漆"
    case woodGrain = "木纹"
    case redSplatter = "红色泼墨"
    
    var id: String { self.rawValue }
    
    /// 纹理图片名称（可选）
    var imageName: String? {
        switch self {
        case .woodGrain, .redSplatter:
            return nil // 临时不使用纹理图片，使用纯色代替
        case .blackLacquer, .brownLacquer, .redLacquer:
            return nil // 纯色材质不需要纹理图片
        }
    }
    
    /// 材质颜色（可选）
    var color: Color? {
        switch self {
        case .blackLacquer:
            return Color.black
        case .brownLacquer:
            return Color.brown
        case .redLacquer:
            return Color.red
        case .woodGrain:
            return Color.brown.opacity(0.7) // 临时使用棕色代替木纹
        case .redSplatter:
            return Color.red.opacity(0.6) // 临时使用半透明红色代替泼墨效果
        }
    }
    
    /// 混合模式
    var blendMode: BlendMode {
        switch self {
        case .blackLacquer, .brownLacquer, .redLacquer:
            return .normal
        case .woodGrain:
            return .multiply
        case .redSplatter:
            return .overlay
        }
    }
    
    /// 缩略图名称（用于选择按钮）
    var thumbnailName: String {
        // 临时不使用缩略图，将在组件中使用颜色显示
        return ""
    }
}

// MARK: - 古琴琴弦数量枚举
enum GuqinStringsCount: String, CaseIterable, Identifiable {
    case sevenStrings = "七弦"
    case fiveStrings = "五弦"
    
    var id: String { self.rawValue }
    
    /// 对应的琴弦图片名称
    var imageName: String {
        // 临时使用现有的古琴图片，实际应该是只有琴弦的透明图片
        return "horizontal_guqin"
    }

    /// 琴弦图标名称（用于选择按钮）
    var iconName: String {
        // 临时使用现有的古琴图片作为占位符
        return "horizontal_guqin"
    }
    
    /// 实际弦数
    var count: Int {
        switch self {
        case .sevenStrings:
            return 7
        case .fiveStrings:
            return 5
        }
    }
}

// MARK: - 定制类别枚举
enum CustomizationCategory: String, CaseIterable, Identifiable {
    case shape = "形制"
    case material = "材质"
    case inscription = "铭文"
    case strings = "琴弦"
    case decoration = "装饰"
    case tuning = "调音"
    
    var id: String { self.rawValue }
    
    /// 类别图标名称
    var iconName: String {
        switch self {
        case .shape:
            return "shape_category_icon"
        case .material:
            return "material_category_icon"
        case .inscription:
            return "inscription_category_icon"
        case .strings:
            return "strings_category_icon"
        case .decoration:
            return "decoration_category_icon"
        case .tuning:
            return "tuning_category_icon"
        }
    }
    
    /// 是否属于琴身类别（左侧边栏）
    var isBodyCategory: Bool {
        switch self {
        case .shape, .material, .inscription:
            return true
        case .strings, .decoration, .tuning:
            return false
        }
    }
}

// MARK: - 古琴配置结构体
struct GuqinConfiguration: Codable, Equatable {
    var shape: GuqinShape
    var material: GuqinMaterialType
    var strings: GuqinStringsCount
    
    /// 默认配置
    static let `default` = GuqinConfiguration(
        shape: .fuxi,
        material: .blackLacquer,
        strings: .sevenStrings
    )
    
    /// 配置名称（用于保存和显示）
    var displayName: String {
        return "\(shape.rawValue) · \(material.rawValue) · \(strings.rawValue)"
    }
}
