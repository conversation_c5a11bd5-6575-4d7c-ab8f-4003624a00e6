//
//  GuqinCustomizationViewModel.swift
//  MAIC-guqin
//
//  Created by AI Assistant on 2025/7/4.
//

import SwiftUI
import Combine

class GuqinCustomizationViewModel: ObservableObject {
    
    // MARK: - Published Properties
    @Published var currentConfiguration: GuqinConfiguration = .default
    @Published var selectedCategory: CustomizationCategory = .shape
    @Published var isPreviewMode: Bool = false
    
    // MARK: - Private Properties
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    init() {
        setupBindings()
    }
    
    // MARK: - Private Methods
    private func setupBindings() {
        // 监听配置变化，可以在这里添加保存逻辑
        $currentConfiguration
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .sink { [weak self] configuration in
                self?.saveConfiguration(configuration)
            }
            .store(in: &cancellables)
    }
    
    private func saveConfiguration(_ configuration: GuqinConfiguration) {
        // TODO: 实现配置保存逻辑
        print("保存配置: \(configuration.displayName)")
    }
    
    // MARK: - Public Methods - 用户操作
    
    /// 选择古琴形制
    func selectShape(_ shape: GuqinShape) {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentConfiguration.shape = shape
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /// 选择古琴材质
    func selectMaterial(_ material: GuqinMaterialType) {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentConfiguration.material = material
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /// 选择琴弦数量
    func selectStrings(_ strings: GuqinStringsCount) {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentConfiguration.strings = strings
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /// 选择定制类别
    func selectCategory(_ category: CustomizationCategory) {
        withAnimation(.easeInOut(duration: 0.2)) {
            selectedCategory = category
        }
        
        // 轻触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    /// 重置配置到默认值
    func resetToDefault() {
        withAnimation(.easeInOut(duration: 0.5)) {
            currentConfiguration = .default
            selectedCategory = .shape
        }
        
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        impactFeedback.impactOccurred()
    }
    
    /// 切换预览模式
    func togglePreviewMode() {
        withAnimation(.easeInOut(duration: 0.3)) {
            isPreviewMode.toggle()
        }
    }
    
    // MARK: - Computed Properties - 方便视图使用
    
    /// 当前选定形制的图片名称
    var currentShapeImageName: String {
        return currentConfiguration.shape.imageName
    }
    
    /// 当前选定材质的图片名称（如果适用）
    var currentMaterialImageName: String? {
        return currentConfiguration.material.imageName
    }
    
    /// 当前选定材质的颜色（如果适用）
    var currentMaterialColor: Color? {
        return currentConfiguration.material.color
    }
    
    /// 当前选定材质的混合模式
    var currentMaterialBlendMode: BlendMode {
        return currentConfiguration.material.blendMode
    }
    
    /// 当前选定弦数的图片名称
    var currentStringsImageName: String {
        return currentConfiguration.strings.imageName
    }
    
    /// 当前选中类别对应的选项列表
    var currentCategoryOptions: [Any] {
        switch selectedCategory {
        case .shape:
            return GuqinShape.allCases
        case .material:
            return GuqinMaterialType.allCases
        case .strings:
            return GuqinStringsCount.allCases
        case .inscription, .decoration, .tuning:
            return [] // 暂时返回空数组，后续可扩展
        }
    }
    
    /// 琴身类别列表（左侧边栏）
    var bodyCategories: [CustomizationCategory] {
        return CustomizationCategory.allCases.filter { $0.isBodyCategory }
    }
    
    /// 琴弦类别列表（右侧边栏）
    var stringsCategories: [CustomizationCategory] {
        return CustomizationCategory.allCases.filter { !$0.isBodyCategory }
    }
    
    /// 检查某个选项是否被选中
    func isSelected<T: Equatable>(_ option: T, in category: CustomizationCategory) -> Bool {
        switch category {
        case .shape:
            if let shape = option as? GuqinShape {
                return currentConfiguration.shape == shape
            }
        case .material:
            if let material = option as? GuqinMaterialType {
                return currentConfiguration.material == material
            }
        case .strings:
            if let strings = option as? GuqinStringsCount {
                return currentConfiguration.strings == strings
            }
        default:
            break
        }
        return false
    }
}
